<template>
  <div class="scene-manager">
    <!-- 主要标签页 -->
    <el-tabs v-model="activeMainTab" type="card" class="scene-main-tabs" @tab-click="handleMainTabClick">
      <!-- 场景列表标签页 -->
      <el-tab-pane label="全部场景" name="list">
        <template slot="label">
          <span>全部场景</span>
        </template>

        <!-- 场景列表工具栏 -->
        <div class="scene-toolbar">
          <div class="search-section">
            <el-input v-model="searchKeyword" placeholder="请过ID/名称/负责人等信息" class="search-input" @input="handleSearch">
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>

          <div class="filter-section">
            <span class="filter-label">场景等级:</span>
            <el-select v-model="levelFilter" placeholder="全部数据" class="filter-select" @change="handleFilter">
              <el-option label="全部数据" value=""></el-option>
              <el-option label="P0" value="P0"></el-option>
              <el-option label="P1" value="P1"></el-option>
              <el-option label="P2" value="P2"></el-option>
            </el-select>
          </div>

          <div class="action-section">
            <el-button icon="el-icon-delete" @click="clearFilters">清除</el-button>
            <el-button icon="el-icon-refresh" @click="$emit('refresh')">刷新</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="createScene">新建场景</el-button>
          </div>
        </div>

        <!-- 场景表格 -->
        <div class="scene-content">
          <div class="scene-table-container">
            <el-table :data="filteredScenes" v-loading="loading" stripe style="width: 100%"
              @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50"></el-table-column>

              <el-table-column prop="id" label="ID" width="80" sortable>
                <template slot-scope="scope">
                  <span class="scene-id">{{ scope.row.id }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="name" label="场景名称" min-width="200" sortable>
                <template slot-scope="scope">
                  <span class="scene-name">{{ scope.row.name }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="level" label="场景等级" width="120" sortable>
                <template slot-scope="scope">
                  <el-tag :type="getLevelTagType(scope.row.level)" class="priority-badge">
                    ⭕ {{ scope.row.level || 'P0' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="status" label="状态" width="100" sortable>
                <template slot-scope="scope">
                  <el-tag :type="getStatusTagType(scope.row.status)" class="status-badge">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="执行结果" width="150">
                <template slot-scope="scope">
                  <el-tag :type="getResultTagType(scope.row.last_result)" class="result-badge">
                    🟢 {{ getResultText(scope.row.last_result) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="tags" label="标签" width="120">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.tags" class="tag-badge"> {{ scope.row.tags }}
                  </el-tag>
                  <span v-else>-</span>
                </template>
              </el-table-column>

              <el-table-column prop="environment" label="场景环境" width="120">
                <template slot-scope="scope">
                  <span>{{ scope.row.environment_name || 'Halo' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" fixed="right">
                <template slot-scope="scope">
                  <div class="actions-cell">
                    <el-button type="text" class="action-btn edit-btn" @click="editScene(scope.row)"> 编辑
                    </el-button>
                    <el-button type="text" class="action-btn run-btn" @click="$emit('execute-scene', scope.row)"> 执行
                    </el-button>
                    <el-button type="text" class="action-btn copy-btn" @click="$emit('copy-scene', scope.row)"> 复制
                    </el-button>
                    <el-dropdown @command="handleCommand" trigger="click">
                      <el-button type="text" class="action-btn more-btn">
                        <i class="el-icon-more"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="{ action: 'delete', scene: scope.row }">删除</el-dropdown-item>
                        <el-dropdown-item :command="{ action: 'export', scene: scope.row }">导出</el-dropdown-item>
                        <el-dropdown-item :command="{ action: 'history', scene: scope.row }">执行历史</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <div class="pagination-info">
              共 {{ total }} 条
            </div>
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
              layout="sizes, prev, pager, next, jumper" :total="total" class="pagination-controls">
            </el-pagination>
          </div>
        </div>
      </el-tab-pane>

      <!-- 动态场景表单标签页 -->
      <el-tab-pane v-for="tab in sceneTabs" :key="tab.name" :label="tab.label" :name="tab.name" :closable="true">
        <template slot="label">
          <span>{{ tab.label }}</span>
        </template>

        <!-- 场景表单头部操作栏 -->
        <div class="scene-form-actions">
          <el-button icon="el-icon-view" title="预览"> </el-button>
          <el-button type="primary" @click="executeScene(tab)" :disabled="tab.mode === 'create'">服务端执行</el-button>
          <el-button type="success" @click="saveScene(tab)">保存</el-button>
        </div>

        <!-- 场景基本信息（编辑模式显示） -->
        <div v-if="tab.mode === 'edit'" class="basic-info-section">
          <div class="basic-info-row">
            <span class="basic-info-label">{{ getStatusText(tab.sceneForm.status) }}</span>
            <span class="priority-indicator">
              <span class="priority-dot" :class="tab.sceneForm.level"></span>
              <span>{{ tab.sceneForm.level || 'P0' }}</span>
            </span>
            <span class="basic-info-value">[{{ tab.sceneForm.id }}] {{ tab.sceneForm.name }}</span>
            <el-button type="text" size="mini" icon="el-icon-star-off"></el-button>
            <el-button type="text" size="mini" icon="el-icon-document"></el-button>
          </div>
          <div class="basic-info-row">
            <span class="basic-info-label">标签</span>
            <span class="basic-info-value">{{ tab.sceneForm.tags || '-' }}</span>
          </div>
          <div class="basic-info-row">
            <span class="basic-info-label">描述</span>
            <span class="basic-info-value">{{ tab.sceneForm.description || '-' }}</span>
          </div>
        </div>

        <!-- 表单内容标签页 -->
        <div class="scene-form-tabs-content">
          <div v-for="formTab in getFormTabs(tab.mode)" :key="formTab.key"
            :class="['scene-form-tab-item', { active: tab.activeTab === formTab.key }]"
            @click="switchFormTab(formTab.key, tab)">
            {{ formTab.label }}
          </div>
          adss
        </div>

        <!-- 表单主体 -->
        <div class="scene-form-body">
          <!-- 步骤管理 -->
          <div v-if="tab.activeTab === 'steps'" class="scene-steps-content">
            <scene-steps-manager :project-id="projectId" :scene-id="tab.sceneForm.id" :steps="tab.sceneForm.steps"
              @steps-change="(steps) => handleStepsChange(steps, tab)" />
          </div>

          <!-- 参数配置 -->
          <div v-if="tab.activeTab === 'params'" class="scene-params-content">
            <scene-params-manager :global-variables="tab.sceneForm.global_variables"
              :global-headers="tab.sceneForm.global_headers"
              @params-change="(params) => handleParamsChange(params, tab)" />
          </div>

          <!-- 前/后置脚本 -->
          <div v-if="tab.activeTab === 'scripts'" class="scene-scripts-content">
            <scene-scripts-manager :pre-script="tab.sceneForm.pre_script" :post-script="tab.sceneForm.post_script"
              @scripts-change="(scripts) => handleScriptsChange(scripts, tab)" />
          </div>

          <!-- 断言配置 -->
          <div v-if="tab.activeTab === 'assertions'" class="scene-assertions-content">
            <scene-assertions-manager :assertions="tab.sceneForm.assertions"
              @assertions-change="(assertions) => handleAssertionsChange(assertions, tab)" />
          </div>

          <!-- 执行历史 -->
          <div v-if="tab.activeTab === 'execution'" class="scene-execution-content">
            <scene-execution-history :project-id="projectId" :scene-id="tab.sceneForm.id" />
          </div>

          <!-- 变更历史 -->
          <div v-if="tab.activeTab === 'changes'" class="scene-changes-content">
            <scene-change-history :project-id="projectId" :scene-id="tab.sceneForm.id" />
          </div>

          <!-- 设置 -->
          <div v-if="tab.activeTab === 'settings'" class="scene-settings-content">
            <scene-settings-manager :settings="tab.sceneForm.settings"
              @settings-change="(settings) => handleSettingsChange(settings, tab)" />
          </div>

          <!-- 右侧表单 -->
          <div class="scene-form-sidebar">
            <el-form :model="tab.sceneForm" :rules="formRules" :ref="`sceneForm_${tab.name}`" label-width="100px">
              <el-form-item label="场景名称" prop="name" required>
                <el-input v-model="tab.sceneForm.name" placeholder="请输入场景名称"></el-input>
              </el-form-item>

              <el-form-item label="所属模块">
                <el-select v-model="tab.sceneForm.module" placeholder="未规划场景" style="width: 100%">
                  <el-option label="未规划场景" value=""></el-option>
                  <el-option label="登录模块" value="login"></el-option>
                  <el-option label="用户模块" value="user"></el-option>
                  <el-option label="文章模块" value="article"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="场景等级">
                <el-radio-group v-model="tab.sceneForm.level">
                  <el-radio label="P0">P0</el-radio>
                  <el-radio label="P1">P1</el-radio>
                  <el-radio label="P2">P2</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="场景状态">
                <el-select v-model="tab.sceneForm.status" style="width: 100%">
                  <el-option label="进行中" value="progress"></el-option>
                  <el-option label="已完成" value="completed"></el-option>
                  <el-option label="待处理" value="pending"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="标签">
                <el-input v-model="tab.sceneForm.tags" placeholder="添加标签，回车键确定"></el-input>
              </el-form-item>

              <el-form-item label="描述">
                <el-input type="textarea" v-model="tab.sceneForm.description" placeholder="请对场景进行描述"
                  :rows="4"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SceneStepsManager from './SceneStepsManager.vue'
import SceneParamsManager from './SceneParamsManager.vue'
import SceneScriptsManager from './SceneScriptsManager.vue'
import SceneAssertionsManager from './SceneAssertionsManager.vue'
import SceneExecutionHistory from './SceneExecutionHistory.vue'
import SceneChangeHistory from './SceneChangeHistory.vue'
import SceneSettingsManager from './SceneSettingsManager.vue'

export default {
  name: 'SceneManager',
  components: {
    SceneStepsManager,
    SceneParamsManager,
    SceneScriptsManager,
    SceneAssertionsManager,
    SceneExecutionHistory,
    SceneChangeHistory,
    SceneSettingsManager
  },
  props: {
    projectId: {
      type: Number,
      required: true
    },
    scenes: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 主标签页状态
      activeMainTab: 'list',

      // 场景表单标签页
      sceneTabs: [],
      tabCounter: 0,

      // 搜索和筛选
      searchKeyword: '',
      levelFilter: '',
      filteredScenes: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 选中项
      selectedScenes: [],

      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState('usercenter', ["userInfo"])
  },
  watch: {
    scenes: {
      handler() {
        this.filterScenes()
      },
      immediate: true
    }
  },
  methods: {
    // 主标签页点击处理
    handleMainTabClick(tab) {
      // 如果点击的是场景表单标签页，需要处理相关逻辑
      if (tab.name !== 'list') {
        // 可以在这里添加标签页切换的逻辑
      }
    },

    // 创建新场景
    createScene() {
      const tabName = `create_${++this.tabCounter}`
      const newTab = {
        name: tabName,
        label: '新建场景',
        mode: 'create',
        activeTab: 'steps',
        sceneForm: {
          name: '',
          module: '',
          level: 'P0',
          status: 'progress',
          tags: '',
          description: '',
          steps: [],
          global_variables: {},
          global_headers: {},
          pre_script: '',
          post_script: '',
          assertions: [],
          settings: {},
          project_id: this.projectId,
          creator: 0,
        }
      }

      this.sceneTabs.push(newTab)
      this.activeMainTab = tabName
    },

    // 编辑场景
    editScene(scene) {
      const tabName = `edit_${scene.id}_${++this.tabCounter}`
      const newTab = {
        name: tabName,
        label: scene.name,
        mode: 'edit',
        activeTab: 'steps',
        sceneForm: { ...scene }
      }

      this.sceneTabs.push(newTab)
      this.activeMainTab = tabName
    },

    // 保存场景
    saveScene(tab) {
      // 验证表单
      if (!tab.sceneForm.name) {
        this.$message.error('请输入场景名称')
        return
      }

      tab.sceneForm.creator = this.userInfo.id
      tab.sceneForm.project_id = this.projectId

      this.$emit('save-scene', tab.sceneForm, tab.mode)
    },

    // 执行场景
    executeScene(tab) {
      if (tab.mode === 'edit' && tab.sceneForm.id) {
        this.$emit('execute-scene', tab.sceneForm)
      }
    },

    // 搜索处理
    handleSearch() {
      this.filterScenes()
    },

    // 筛选处理
    handleFilter() {
      this.filterScenes()
    },

    // 筛选场景
    filterScenes() {
      let filtered = [...this.scenes]

      // 关键词搜索
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(scene =>
          scene.name.toLowerCase().includes(keyword) ||
          scene.id.toString().includes(keyword) ||
          (scene.description && scene.description.toLowerCase().includes(keyword))
        )
      }

      // 等级筛选
      if (this.levelFilter) {
        filtered = filtered.filter(scene => scene.level === this.levelFilter)
      }

      this.filteredScenes = filtered
      this.total = filtered.length
    },

    // 清除筛选
    clearFilters() {
      this.searchKeyword = ''
      this.levelFilter = ''
      this.filterScenes()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.handleCurrentChange(1)
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 选择处理
    handleSelectionChange(selection) {
      this.selectedScenes = selection
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      const { action, scene } = command
      switch (action) {
        case 'delete':
          this.$emit('delete-scene', scene)
          break
        case 'export':
          this.exportScene(scene)
          break
        case 'history':
          this.viewHistory(scene)
          break
      }
    },

    // 导出场景
    exportScene(scene) {
      this.$message.info(`导出场景 "${scene.name}" 功能开发中`)
    },

    // 查看执行历史
    viewHistory(scene) {
      this.$message.info(`查看场景 "${scene.name}" 执行历史功能开发中`)
    },

    // 场景表单相关方法
    // 处理步骤变更
    handleStepsChange(steps, tab) {
      tab.sceneForm.steps = steps
    },

    // 处理参数变更
    handleParamsChange(params, tab) {
      tab.sceneForm.global_variables = params.variables
      tab.sceneForm.global_headers = params.headers
    },

    // 处理脚本变更
    handleScriptsChange(scripts, tab) {
      tab.sceneForm.pre_script = scripts.preScript
      tab.sceneForm.post_script = scripts.postScript
    },

    // 处理断言变更
    handleAssertionsChange(assertions, tab) {
      tab.sceneForm.assertions = assertions
    },

    // 处理设置变更
    handleSettingsChange(settings, tab) {
      tab.sceneForm.settings = settings
    },

    // 切换场景表单内部标签页
    switchFormTab(tabKey, tab) {
      tab.activeTab = tabKey
    },

    // 获取表单标签页配置
    getFormTabs(mode) {
      const baseTabs = [
        { key: 'steps', label: '步骤' },
        { key: 'params', label: '参数' },
        { key: 'scripts', label: '前/后置' },
        { key: 'assertions', label: '断言' },
        { key: 'settings', label: '设置' }
      ]

      if (mode === 'edit') {
        baseTabs.splice(4, 0,
          { key: 'execution', label: '执行历史' },
          { key: 'changes', label: '变更历史' }
        )
      }

      return baseTabs
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        'P0': 'danger',
        'P1': 'warning',
        'P2': 'info'
      }
      return typeMap[level] || 'danger'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'progress': 'warning',
        'completed': 'success',
        'pending': 'info'
      }
      return typeMap[status] || 'warning'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'progress': '进行中',
        'completed': '已完成',
        'pending': '待处理'
      }
      return textMap[status] || '进行中'
    },

    // 获取结果标签类型
    getResultTagType(result) {
      const typeMap = {
        'success': 'success',
        'failed': 'danger',
        'running': 'warning'
      }
      return typeMap[result] || 'success'
    },

    // 获取结果文本
    getResultText(result) {
      const textMap = {
        'success': '成功',
        'failed': '失败',
        'running': '运行中'
      }
      return textMap[result] || '成功'
    }
  }
}
</script>

<style scoped>
.scene-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

/* 主标签页样式 */
.scene-main-tabs {
  height: 100%;
}

.scene-main-tabs>>>.el-tabs__content {
  height: calc(100% - 40px);
  overflow: hidden;
}

.scene-main-tabs>>>.el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 场景表单操作栏 */
.scene-form-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
  justify-content: flex-end;
}

/* 基本信息区域 */
.basic-info-section {
  padding: 16px;
  background: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.basic-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.basic-info-row:last-child {
  margin-bottom: 0;
}

.basic-info-label {
  font-size: 12px;
  color: #666;
  background: #e6f7ff;
  padding: 2px 8px;
  border-radius: 4px;
  min-width: 60px;
  text-align: center;
}

.priority-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.priority-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4d4f;
}

.priority-dot.P1 {
  background: #faad14;
}

.priority-dot.P2 {
  background: #52c41a;
}

.basic-info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

/* 表单内容标签页 */
.scene-form-tabs-content {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background: #fff;
}

.scene-form-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.scene-form-tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.scene-form-tab-item:hover {
  color: #1890ff;
}

/* 表单主体 */
.scene-form-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.scene-steps-content,
.scene-params-content,
.scene-scripts-content,
.scene-assertions-content,
.scene-execution-content,
.scene-changes-content,
.scene-settings-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 右侧表单 */
.scene-form-sidebar {
  width: 300px;
  padding: 16px;
  border-left: 1px solid #e0e0e0;
  background: #fafafa;
  overflow-y: auto;
}

.scene-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 16px;
  flex-wrap: wrap;
  border-bottom: 1px solid #e0e0e0;
}

.search-section {
  flex: 1;
  min-width: 200px;
}

.search-input {
  max-width: 300px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-select {
  width: 120px;
}

.action-section {
  display: flex;
  gap: 8px;
}

/* 内容区域 */
.scene-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.scene-table-container {
  flex: 1;
  overflow: auto;
}

/* 表格样式 */
.scene-id {
  font-weight: 500;
  color: #1890ff;
}

.scene-name {
  font-weight: 500;
  color: #333;
}

.priority-badge {
  font-weight: 500;
}

.status-badge,
.result-badge,
.tag-badge {
  font-size: 12px;
}

.actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.edit-btn {
  color: #1890ff;
}

.run-btn {
  color: #52c41a;
}

.copy-btn {
  color: #fa8c16;
}

.more-btn {
  color: #666;
}

/* 分页区域 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.pagination-controls {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .scene-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-section,
  .filter-section,
  .action-section {
    justify-content: flex-start;
  }

  .scene-form-body {
    flex-direction: column;
  }

  .scene-form-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e0e0e0;
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .scene-toolbar {
    padding: 8px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .scene-form-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .basic-info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .scene-form-tabs-content {
    flex-wrap: wrap;
  }

  .scene-form-tab-item {
    padding: 8px 12px;
    font-size: 12px;
  }
}
</style>
